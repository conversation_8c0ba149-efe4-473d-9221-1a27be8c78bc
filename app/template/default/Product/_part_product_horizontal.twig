<div class="mx-auto px-[5%] md:px-[0%] w-full max-w-[960px]">
    <div class="flex flex-col md:flex-row">
        <div class="flex flex-row md:w-[68%]">
            <div class="flex-[1]">
                <a target="_blank" href="{{ url('product_detail', {id : Product.id} ) }}">
                    {% set image = asset(Product.MainListImage|no_image_product, 'save_image') %}
                    <img class="object-cover rounded-2xl w-full aspect-square" src="{{ is_mobile() ? image | imagine_filter('resize') : image }}" alt="{{ Product.name }}">
                </a>
            </div>
            <div class="flex-[3] md:flex-[5] flex flex-col ml-[5%] md:ml-[3%] md:py-2">
                <a class="font-semibold tracking-[.06em] text-[13px] md:text-[14px]" target="_blank" href="{{ url('product_detail', {id : Product.id} ) }}">{{ Product.name }}
                    {% if form.vars.product_detail.product_option_items %}
                        (
                        {{ form.vars.product_detail.product_option_items }}
                        )
                    {% endif %}
                </a>
                <div>
                    {% if form.vars.data.ProductClass.ClassCategory1 and form.vars.data.ProductClass.ClassCategory1.id %}
                        <p class="text-[11px] md:text-[12px]">
                            {{ form.vars.data.ProductClass.ClassCategory1.ClassName.name }}
                            {% if form.vars.data.ProductClass.ClassCategory2 %}
                                /
                                {{ form.vars.data.ProductClass.ClassCategory2.ClassName.name }}
                            {% endif %}
                            ：{{ form.vars.data.ProductClass.ClassCategory1 }}
                            {% if form.vars.data.ProductClass.ClassCategory2 %}
                                /
                                {{ form.vars.data.ProductClass.ClassCategory2 }}
                            {% endif %}
                        </p>
                    {% endif %}
                    {% if form.vars.data.ProductClass.price01_inc_tax %}
                        <p class="text-[16px] md:text-[18px]">
                            <span class="line-through">
                                {{form.vars.data.ProductClass.price01_inc_tax|number_format(0, '.', ',') }}
                                <span class="font-semibold">円</span>
                            </span>
                            <span class="text-[11px] md:text-[12px]">(<span class="font-semibold">税込</span>)</span>
                        </p>
                        <p class="text-[11px] md:text-[12px] text-[red]">
                            {{form.vars.data.ProductClass.price02_inc_tax|number_format(0, '.', ',') }}
                            <span class="font-semibold">円</span>
                            <span class="text-[11px] md:text-[12px]">(<span class="font-semibold">税込</span>)</span>
                        </p>
                    {% else %}
                        <p class="text-[16px] md:text-[18px]">
                            {{ form.vars.data.ProductClass.price02_inc_tax|number_format(0, '.', ',') }}
                            <span class="font-semibold">円</span>
                            <span class="text-[11px] md:text-[12px]">(<span class="font-semibold">税込</span>)</span>
                        </p>
                    {% endif %}
                    {% if Product.stock_find == 0 %}
                        <span class="text-[11px] md:text-[12px] text-[red]">
                            Sold out
                        </span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    <div class="flex justify-end items-center py-[20px]">
        {% if form is defined %}
            <p class="add-cart-btn cursor-pointer text-[#333333] font-semibold tracking-[.06em] text-[13px] md:text-[14px] hover:opacity-50 underline underline-offset-2 bg-transparent border-0 p-0 mr-3
                {% if Product.stock_find == 0 %}pointer-events-none opacity-50{% endif %}"
                data-product-id="{{ form.product_id.vars.value }}"
                data-product-class-id="{{ form.vars.product_detail.product_class_id }}"
                data-quantity="{{ form.vars.product_detail.quantity }}"
                data-product-option-id="{{ form.vars.product_detail.option_id }}"
                data-product-option-quantity="{{ form.vars.product_detail.option_quantity }}"
                data-product-option-items="{{ form.vars.product_detail.product_option_items }}"
                data-token="{{ csrf_token('add_cart') }}"
                data-add-cart-url="{{ url('product_add_cart', {'id': Product.id}) }}">
            カートに戻す
            </p>
        {% else %}
            <p class="cursor-pointer text-[#333333] font-semibold tracking-[.06em] text-[13px] md:text-[14px] hover:opacity-50 mr-3 underline underline-offset-2">あとで買う</p>
        {% endif %}
        |
        <a class="flex items-center w-full justify-end max-w-[110px] h-[30px] md:max-w-[68px] md:h-[34px]" href="{{ url('cart_buy_later_delete', { id : Product.id, type: type }) }}" {{ csrf_token_for_anchor() }} data-method="delete">
            <div class="text-[11px] md:text-[14px] w-full h-full items-center hover:opacity-50 flex">
                <button class="text-[#333333] rounded font-semibold tracking-[.06em] text-[13px] md:text-[14px] hover:opacity-50 ml-3 underline underline-offset-2 items-center" type="submit">消去</button>
            </div>
        </a>
    </div>
    <div class="cart-buy-later"></div>
</div>
