{#
 * This file is part of the Recommend Product plugin
 *
 * Copyright (C) EC-CUBE CO.,LTD. All Rights Reserved.
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
#}

<!-- CART RECOMMEND -->
{% if products_later|length %}
    <div class="max-w-[960px] w-full mx-auto pb-10">
        <div class="bg-[#bababa] h-[1px]"></div>
    </div>
    <p class="w-fit text-[15px] md:text-[16px] text-[#333333] mt-4">あとで買うに入っている商品</p>
    <div class="mx-auto w-full max-w-[960px] pb-10">
        <ul class="max-w-[960px] m-auto mt-[5px]">
            {% for LaterProduct in products_later|slice(0, 8) %}
                {% set Product = LaterProduct.Product %}
                {% set form = forms['later_' ~ Product.id] %}
                <li class="w-full flex py-3">
                    {{ include('/Product/_part_product_horizontal.twig', {Product: Product, form: form, type: constant('Eccube\\Entity\\CustomerFavoriteProduct::TYPE_LATER') }) }}
                </li>
            {% endfor %}
        </ul>
    </div>
{% endif %}

{% if products_favorite|length %}
    <div class="max-w-[960px] w-full mx-auto pb-10">
        <div class="bg-[#bababa] h-[1px]"></div>
    </div>
    <p class="w-fit text-[15px] md:text-[16px] text-[#333333] mt-4">お気に入り</p>
    <div class="mx-auto w-full max-w-[960px] pb-10">
        <ul class="max-w-[960px] m-auto mt-[5px]">
            {% for FavoriteProduct in products_favorite|slice(0, 4) %}
                {% set Product = FavoriteProduct.Product %}
                {% set form = forms['favorite_' ~ Product.id] %}
                <li class="w-full flex py-3">
                    {{ include('/Product/_part_product_horizontal.twig', {Product: Product, form: form, type: constant('Eccube\\Entity\\CustomerFavoriteProduct::TYPE_FAVORITE') }) }}
                </li>
            {% endfor %}
        </ul>
    </div>
{% endif %}
<script>
$(document).ready(function() {
    $(document).on('click', '.add-cart-btn', function(e) {
        e.preventDefault();
        let $btn = $(this),
        data = {
            'product_id': $btn.data('product-id'),
            'ProductClass': $btn.data('product-class-id'),
            'product_option_items': $btn.data('product-option-items'),
            'quantity': $btn.data('quantity'),
            'product_option_id': $btn.data('product-option-id'),
            'product_option_quantity': $btn.data('product-option-quantity'),
            '_token': $btn.data('token')
        };
        $form = $('<form method="POST" style="display:none;"></form>')
            .attr('action', $btn.data('add-cart-url'));
        $.each(data, function(name, value) {
            $form.append($('<input type="hidden">').attr('name', name).val(value));
        });
        $('body').append($form);
        $form.submit();
    });
});
</script>
