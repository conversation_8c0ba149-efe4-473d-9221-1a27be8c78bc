<?php

namespace Customize\Entity;
use Eccube\Annotation\EntityExtension;
use Doctrine\ORM\Mapping as ORM;

/**
* @EntityExtension("Eccube\Entity\CustomerFavoriteProduct")
*/
trait CustomerFavoriteProductTrait
{
    // タイプの定数
    const TYPE_FAVORITE = 1; // お気に入り
    const TYPE_LATER = 2;    // あとで買う

    /**
     * @var int
     *
     * @ORM\Column(name="type", type="smallint", options={"unsigned":true, "default":1})
     */
    private $type = self::TYPE_FAVORITE;


    /**
     * Set type.
     *
     * @param int $type
     *
     * @return CustomerFavoriteProduct
     */
    public function setType($type)
    {
        $this->type = $type;

        return $this;
    }

    /**
     * Get type.
     *
     * @return int
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * @var string|null
     *
     * @ORM\Column(name="product_detail", type="text", nullable=true)
     */
    private $product_detail;

    /**
     * Set product_detail.
     *
     * @param string|null $product_detail
     *
     * @return News
     */
    public function setProductDetail($product_detail = null)
    {
        $this->product_detail = $product_detail;

        return $this;
    }

    /**
     * Get product_detail.
     *
     * @return string|null
     */
    public function getProductDetail()
    {
        return $this->product_detail;
    }

   /**
     * Get productDetail (decoded array)
     *
     * @return array
     */
    public function getProductDetailArray()
    {
        if (empty($this->product_detail)) {
            return [];
        }

        $decoded = json_decode($this->product_detail, true);

        return $decoded ?: [];
    }
}
